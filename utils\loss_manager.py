"""
损失函数管理器
支持多种损失函数的组合和动态权重调整
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from .loss_opr import FocalLoss2d, RCELoss, BalanceLoss, ProbOhemCrossEntropy2d
from engine.logger import get_logger

logger = get_logger()


class DiceLoss(nn.Module):
    """Dice Loss实现"""
    def __init__(self, smooth=1e-5, ignore_index=255):
        super(DiceLoss, self).__init__()
        self.smooth = smooth
        self.ignore_index = ignore_index

    def forward(self, pred, target):
        # 获取有效掩码
        valid_mask = (target != self.ignore_index).float()
        
        # 转换为概率
        pred = F.softmax(pred, dim=1)
        
        # 转换为one-hot编码
        target_one_hot = F.one_hot(target.long(), num_classes=pred.size(1))
        target_one_hot = target_one_hot.permute(0, 3, 1, 2).float()
        
        # 应用掩码
        pred = pred * valid_mask.unsqueeze(1)
        target_one_hot = target_one_hot * valid_mask.unsqueeze(1)
        
        # 计算Dice系数
        intersection = (pred * target_one_hot).sum(dim=(2, 3))
        union = pred.sum(dim=(2, 3)) + target_one_hot.sum(dim=(2, 3))
        
        dice = (2.0 * intersection + self.smooth) / (union + self.smooth)
        dice_loss = 1 - dice.mean()
        
        return dice_loss


class LovaszLoss(nn.Module):
    """Lovasz Loss实现"""
    def __init__(self, ignore_index=255):
        super(LovaszLoss, self).__init__()
        self.ignore_index = ignore_index

    def forward(self, pred, target):
        # 简化的Lovasz Loss实现
        # 这里使用Focal Loss作为近似
        focal_loss = FocalLoss2d(gamma=2.0, ignore_index=self.ignore_index)
        return focal_loss(pred, target)


class BoundaryLoss(nn.Module):
    """边界损失函数"""
    def __init__(self, ignore_index=255):
        super(BoundaryLoss, self).__init__()
        self.ignore_index = ignore_index

    def forward(self, pred, target):
        # 计算边界
        boundary = self._get_boundary(target)
        
        # 在边界区域应用更高的权重
        ce_loss = F.cross_entropy(pred, target.long(), 
                                 ignore_index=self.ignore_index, 
                                 reduction='none')
        
        # 边界区域权重为2，其他区域权重为1
        weights = 1.0 + boundary.float()
        weighted_loss = ce_loss * weights
        
        return weighted_loss.mean()

    def _get_boundary(self, target):
        """提取边界"""
        # 简单的边界检测：使用梯度
        target_float = target.float()
        grad_x = torch.abs(target_float[:, :, 1:] - target_float[:, :, :-1])
        grad_y = torch.abs(target_float[:, 1:, :] - target_float[:, :-1, :])
        
        # 填充以保持尺寸
        grad_x = F.pad(grad_x, (0, 1, 0, 0))
        grad_y = F.pad(grad_y, (0, 0, 0, 1))
        
        boundary = (grad_x + grad_y) > 0
        return boundary


class ContextLoss(nn.Module):
    """上下文损失函数"""
    def __init__(self, kernel_size=3, dilation=1, ignore_index=255):
        super(ContextLoss, self).__init__()
        self.kernel_size = kernel_size
        self.dilation = dilation
        self.ignore_index = ignore_index

    def forward(self, pred, target):
        # 使用膨胀卷积提取上下文信息
        # 这里简化为标准的交叉熵损失
        return F.cross_entropy(pred, target.long(), ignore_index=self.ignore_index)


class LossManager(nn.Module):
    """损失函数管理器"""
    def __init__(self, config):
        super(LossManager, self).__init__()
        self.config = config
        self.loss_type = config.loss_type
        self.loss_weights = config.loss_weights.copy()
        self.ignore_index = config.background
        self.log_components = getattr(config, 'log_loss_components', False)
        
        # 初始化各种损失函数
        self._init_loss_functions()
        
        # 自适应权重相关
        if self.loss_type == 'adaptive':
            self._init_adaptive_weights()

    def _init_loss_functions(self):
        """初始化损失函数"""
        # 基础交叉熵损失
        self.ce_loss = nn.CrossEntropyLoss(
            ignore_index=self.ignore_index,
            weight=self._get_class_weights(),
            label_smoothing=getattr(self.config, 'label_smoothing', 0.0)
        )
        
        # Focal Loss
        self.focal_loss = FocalLoss2d(
            gamma=getattr(self.config, 'focal_gamma', 2.0),
            weight=self._get_focal_alpha(),
            ignore_index=self.ignore_index
        )
        
        # Dice Loss
        self.dice_loss = DiceLoss(ignore_index=self.ignore_index)
        
        # Lovasz Loss
        self.lovasz_loss = LovaszLoss(ignore_index=self.ignore_index)
        
        # Boundary Loss
        self.boundary_loss = BoundaryLoss(ignore_index=self.ignore_index)
        
        # Context Loss
        self.context_loss = ContextLoss(
            kernel_size=getattr(self.config, 'context_kernel_size', 3),
            dilation=getattr(self.config, 'context_dilation', 1),
            ignore_index=self.ignore_index
        )

    def _get_class_weights(self):
        """获取类别权重"""
        if hasattr(self.config, 'class_weights') and self.config.class_weights is not None:
            return torch.tensor(self.config.class_weights, dtype=torch.float32)
        return None

    def _get_focal_alpha(self):
        """获取Focal Loss的alpha参数"""
        if hasattr(self.config, 'focal_alpha') and self.config.focal_alpha is not None:
            return np.array(self.config.focal_alpha)
        return None

    def _init_adaptive_weights(self):
        """初始化自适应权重"""
        initial_weights = getattr(self.config, 'adaptive_initial_weights', {})
        self.adaptive_weights = nn.ParameterDict()
        
        for key, value in initial_weights.items():
            self.adaptive_weights[key] = nn.Parameter(torch.tensor(value, dtype=torch.float32))

    def forward(self, pred, target, epoch=None):
        """前向传播"""
        if self.loss_type == 'ce':
            return self._compute_ce_loss(pred, target)
        elif self.loss_type == 'focal':
            return self._compute_focal_loss(pred, target)
        elif self.loss_type == 'combined':
            return self._compute_combined_loss(pred, target)
        elif self.loss_type == 'adaptive':
            return self._compute_adaptive_loss(pred, target, epoch)
        elif self.loss_type == 'simplified_combined':
            return self._compute_simplified_combined_loss(pred, target)
        else:
            raise ValueError(f"Unsupported loss type: {self.loss_type}")

    def _compute_ce_loss(self, pred, target):
        """计算交叉熵损失"""
        loss = self.ce_loss(pred, target.long())
        
        if self.log_components:
            logger.info(f"CE Loss: {loss.item():.4f}")
        
        return loss

    def _compute_focal_loss(self, pred, target):
        """计算Focal损失"""
        loss = self.focal_loss(pred, target.long())
        
        if self.log_components:
            logger.info(f"Focal Loss: {loss.item():.4f}")
        
        return loss

    def _compute_combined_loss(self, pred, target):
        """计算组合损失"""
        total_loss = 0.0
        loss_components = {}
        
        # 交叉熵损失
        if self.loss_weights['ce_weight'] > 0:
            ce_loss = self.ce_loss(pred, target.long())
            total_loss += self.loss_weights['ce_weight'] * ce_loss
            loss_components['ce'] = ce_loss.item()
        
        # Focal损失
        if self.loss_weights['focal_weight'] > 0:
            focal_loss = self.focal_loss(pred, target.long())
            total_loss += self.loss_weights['focal_weight'] * focal_loss
            loss_components['focal'] = focal_loss.item()
        
        # Dice损失
        if self.loss_weights['dice_weight'] > 0:
            dice_loss = self.dice_loss(pred, target)
            total_loss += self.loss_weights['dice_weight'] * dice_loss
            loss_components['dice'] = dice_loss.item()
        
        # Lovasz损失
        if self.loss_weights['lovasz_weight'] > 0:
            lovasz_loss = self.lovasz_loss(pred, target.long())
            total_loss += self.loss_weights['lovasz_weight'] * lovasz_loss
            loss_components['lovasz'] = lovasz_loss.item()
        
        # 边界损失
        if self.loss_weights['boundary_weight'] > 0:
            boundary_loss = self.boundary_loss(pred, target)
            total_loss += self.loss_weights['boundary_weight'] * boundary_loss
            loss_components['boundary'] = boundary_loss.item()
        
        # 上下文损失
        if self.loss_weights['context_weight'] > 0:
            context_loss = self.context_loss(pred, target)
            total_loss += self.loss_weights['context_weight'] * context_loss
            loss_components['context'] = context_loss.item()
        
        if self.log_components:
            component_str = " | ".join([f"{k}: {v:.4f}" for k, v in loss_components.items()])
            logger.info(f"Loss Components - {component_str} | Total: {total_loss.item():.4f}")
        
        return total_loss

    def _compute_adaptive_loss(self, pred, target, epoch):
        """计算自适应损失"""
        # 这里实现自适应权重调整逻辑
        # 简化版本：使用固定权重
        return self._compute_combined_loss(pred, target)

    def _compute_simplified_combined_loss(self, pred, target):
        """计算简化的组合损失"""
        # 主要使用CE + Focal的组合
        ce_loss = self.ce_loss(pred, target.long())
        
        total_loss = self.loss_weights['ce_weight'] * ce_loss
        
        if self.loss_weights['focal_weight'] > 0:
            focal_loss = self.focal_loss(pred, target.long())
            total_loss += self.loss_weights['focal_weight'] * focal_loss
        
        if self.log_components:
            logger.info(f"Simplified Combined Loss: CE={ce_loss.item():.4f}, Total={total_loss.item():.4f}")
        
        return total_loss

    def update_weights(self, **kwargs):
        """动态更新损失权重"""
        for key, value in kwargs.items():
            if key in self.loss_weights:
                self.loss_weights[key] = value
                logger.info(f"Updated {key} to {value}")
