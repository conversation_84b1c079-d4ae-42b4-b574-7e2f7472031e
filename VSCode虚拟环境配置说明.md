# VSCode虚拟环境配置说明

## 问题解决
已为您配置VSCode使用位于 `D:\pycharm exercise\CMX_main\.venv` 的虚拟环境。

## 配置文件说明

### 1. `.vscode/settings.json`
- 设置默认Python解释器路径
- 配置终端自动激活虚拟环境
- 设置环境变量和路径

### 2. `.vscode/launch.json`
- 配置调试和运行设置
- 指定正确的Python解释器

### 3. `.vscode/tasks.json`
- 提供快速任务来激活虚拟环境
- 可通过 Ctrl+Shift+P -> "Tasks: Run Task" 使用

### 4. `.env`
- 环境变量配置文件
- 设置PYTHONPATH和虚拟环境路径

## 使用方法

### 方法1: 使用新的终端配置
1. 在VSCode中打开新终端 (Ctrl+Shift+`)
2. 选择 "PowerShell with Venv" 配置文件
3. 虚拟环境将自动激活

### 方法2: 使用批处理脚本
运行项目根目录下的：
- `activate_env.bat` (Windows批处理)
- `activate_env.ps1` (PowerShell脚本)

### 方法3: 手动激活
在终端中运行：
```powershell
& "D:\pycharm exercise\CMX_main\.venv\Scripts\Activate.ps1"
```

## 验证配置
激活虚拟环境后，运行以下命令验证：
```bash
python --version
where python
```

应该显示虚拟环境中的Python路径。

## 注意事项
- 如果遇到执行策略问题，请以管理员身份运行PowerShell并执行：
  ```powershell
  Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
  ```
- 重启VSCode以确保所有配置生效
- 确保虚拟环境路径 `D:\pycharm exercise\CMX_main\.venv` 存在且有效
